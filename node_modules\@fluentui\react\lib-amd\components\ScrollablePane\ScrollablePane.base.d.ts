import * as React from 'react';
import { Sticky } from '../../Sticky';
import type { IScrollablePane, IScrollablePaneProps } from './ScrollablePane.types';
export interface IScrollablePaneState {
    stickyTopHeight: number;
    stickyBottomHeight: number;
    scrollbarWidth: number;
    scrollbarHeight: number;
}
export declare class ScrollablePaneBase extends React.Component<IScrollablePaneProps, IScrollablePaneState> implements IScrollablePane {
    static contextType: React.Context<import("@fluentui/react-window-provider").WindowProviderProps>;
    private _root;
    private _stickyAboveRef;
    private _stickyBelowRef;
    private _contentContainer;
    private _subscribers;
    private _stickies;
    private _mutationObserver;
    private _notifyThrottled;
    private _async;
    private _events;
    constructor(props: IScrollablePaneProps);
    get root(): HTMLDivElement | null;
    get stickyAbove(): HTMLDivElement | null;
    get stickyBelow(): HTMLDivElement | null;
    get contentContainer(): HTMLDivElement | null;
    componentDidMount(): void;
    componentWillUnmount(): void;
    shouldComponentUpdate(nextProps: IScrollablePaneProps, nextState: IScrollablePaneState): boolean;
    componentDidUpdate(prevProps: IScrollablePaneProps, prevState: IScrollablePaneState): void;
    render(): JSX.Element;
    setStickiesDistanceFromTop(): void;
    forceLayoutUpdate(): void;
    subscribe: (handler: Function) => void;
    unsubscribe: (handler: Function) => void;
    addSticky: (sticky: Sticky) => void;
    removeSticky: (sticky: Sticky) => void;
    sortSticky: (sticky: Sticky, sortAgain?: boolean) => void;
    updateStickyRefHeights: () => void;
    notifySubscribers: () => void;
    getScrollPosition: () => number;
    syncScrollSticky: (sticky: Sticky) => void;
    private _getScrollablePaneContext;
    private _checkStickyStatus;
    private _addToStickyContainer;
    private _removeStickyFromContainers;
    private _onWindowResize;
    private _getStickyContainerStyle;
    private _getScrollbarWidth;
    private _getScrollbarHeight;
    private _onScroll;
}
