import { DirectionalHint } from '../../common/DirectionalHint';
import { RectangleEdge } from './positioning.types';
import { Rectangle } from '../../Utilities';
import type { IRectangle, Point } from '../../Utilities';
import type { IPositionDirectionalHintData, IPositionedData, ICalloutPositionedInfo, ICalloutBeakPositionedInfo, IPositionProps, IWindowWithSegments } from './positioning.types';
/**
 * Do not call methods from this directly, use either positionCallout or positionElement or make another function that
 * utilizes them.
 * START Private functions and interfaces
 */
export interface IElementPosition {
    elementRectangle: Rectangle;
    targetEdge: RectangleEdge;
    alignmentEdge: RectangleEdge | undefined;
    forcedInBounds?: boolean;
}
export interface IElementPositionInfo extends IElementPosition {
    targetRectangle: Rectangle;
}
declare function _calculateActualBeakWidthInPixels(beakWidth: number): number;
/**
 * Returns the appropriate IPositionData based on the props altered for RTL.
 * If directionalHintForRTL is passed in that is used if the page is RTL.
 * If directionalHint is specified, no directionalHintForRTL is available, and the page is RTL, the hint will be
 * flipped (e.g. bottomLeftEdge would become bottomRightEdge).
 *
 * If there is no directionalHint passed in, bottomAutoEdge is chosen automatically.
 */
declare function _getPositionData(directionalHint?: DirectionalHint, directionalHintForRTL?: DirectionalHint, previousPositions?: IPositionDirectionalHintData): IPositionDirectionalHintData;
declare function _positionElementWithinBounds(elementToPosition: Rectangle, target: Rectangle, bounding: Rectangle, positionData: IPositionDirectionalHintData, gap: number, shouldScroll?: boolean, minimumScrollResizeHeight?: number, directionalHintFixed?: boolean, coverTarget?: boolean): IElementPosition;
declare function _finalizeBeakPosition(elementPosition: IElementPositionInfo, positionedBeak: Rectangle, bounds?: Rectangle): ICalloutBeakPositionedInfo;
declare function _positionBeak(beakWidth: number, elementPosition: IElementPositionInfo): Rectangle;
/**
 * If max height is less than zero it returns the bounds height instead.
 */
declare function _getMaxHeightFromTargetRectangle(targetRectangle: Rectangle, targetEdge: DirectionalHint, gapSpace: number, bounds: Rectangle, coverTarget?: boolean): number;
declare function _finalizePositionData(positionedElement: IElementPosition, hostElement: HTMLElement, bounds?: Rectangle, coverTarget?: boolean, doNotFinalizeReturnEdge?: boolean): IPositionedData;
export declare const __positioningTestPackage: {
    _finalizePositionData: typeof _finalizePositionData;
    _finalizeBeakPosition: typeof _finalizeBeakPosition;
    _calculateActualBeakWidthInPixels: typeof _calculateActualBeakWidthInPixels;
    _positionElementWithinBounds: typeof _positionElementWithinBounds;
    _positionBeak: typeof _positionBeak;
    _getPositionData: typeof _getPositionData;
    _getMaxHeightFromTargetRectangle: typeof _getMaxHeightFromTargetRectangle;
};
/**
 * Used to position an element relative to the given positioning props.
 * If positioning has been completed before, previousPositions can be passed to ensure that the positioning element
 * repositions based on its previous targets rather than starting with directionalhint.
 */
export declare function positionElement(props: IPositionProps, hostElement: HTMLElement, elementToPosition: HTMLElement, previousPositions?: IPositionedData, win?: Window): IPositionedData;
export declare function positionCallout(props: IPositionProps, hostElement: HTMLElement, elementToPosition: HTMLElement, previousPositions?: ICalloutPositionedInfo, shouldScroll?: boolean, minimumScrollResizeHeight?: number, win?: Window): ICalloutPositionedInfo;
export declare function positionCard(props: IPositionProps, hostElement: HTMLElement, elementToPosition: HTMLElement, previousPositions?: ICalloutPositionedInfo, win?: Window): ICalloutPositionedInfo;
/**
 * Gets the maximum height that a rectangle can have in order to fit below or above a target.
 * If the directional hint specifies a left or right edge (i.e. leftCenter) it will limit the height to the topBorder
 * of the target given.
 * If no bounds are provided then the window is treated as the bounds.
 */
export declare function getMaxHeight(target: Element | MouseEvent | Point | Rectangle, targetEdge: DirectionalHint, gapSpace?: number, bounds?: IRectangle, coverTarget?: boolean, win?: Window): number;
/**
 * Returns the opposite edge of the given RectangleEdge.
 */
export declare function getOppositeEdge(edge: RectangleEdge): RectangleEdge;
export declare function getBoundsFromTargetWindow(target: Element | MouseEvent | Point | Rectangle | null, targetWindow: IWindowWithSegments): IRectangle;
export declare function calculateGapSpace(isBeakVisible: boolean | undefined, beakWidth: number | undefined, gapSpace: number | undefined): number;
export declare function getRectangleFromTarget(target: Element | MouseEvent | Point | Rectangle): Rectangle;
export {};
