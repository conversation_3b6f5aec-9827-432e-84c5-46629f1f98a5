import type { IStyle } from '@fluentui/style-utilities';
import type { Theme } from '@fluentui/theme';
export type StylesClassMapping<TStyleSet extends {
    [key in keyof TStyleSet]: IStyle;
}> = {
    [key in keyof TStyleSet]: string;
};
/**
 * Options that can be provided to the hook generated by `makeStyles`.
 * @deprecated Only used in deprecated `makeStyles` implementation below.
 */
export type UseStylesOptions = {
    theme?: Theme;
};
/**
 * Registers a css object, optionally as a function of the theme.
 *
 * @param styleOrFunction - Either a css javascript object, or a function which takes in `ITheme`
 * and returns a css javascript object.
 *
 * @deprecated Use `mergeStyles` instead for v8 related code. We will be using a new implementation of `makeStyles` in
 * future versions of the library.
 */
export declare function makeStyles<TStyleSet extends {
    [key in keyof TStyleSet]: IStyle;
} = {
    [key: string]: IStyle;
}>(styleOrFunction: TStyleSet | ((theme: Theme) => TStyleSet)): (options?: UseStylesOptions) => StylesClassMapping<TStyleSet>;
