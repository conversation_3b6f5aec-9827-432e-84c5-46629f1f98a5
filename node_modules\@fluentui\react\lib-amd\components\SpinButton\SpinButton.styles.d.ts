import type { ITheme } from '../../Styling';
import type { IButtonStyles } from '../../Button';
import type { ISpinButtonStyles, ISpinButtonStyleProps } from './SpinButton.types';
export declare const getArrowButtonStyles: (theme: ITheme, isUpArrow: boolean, customSpecificArrowStyles?: Partial<IButtonStyles>) => IButtonStyles;
export declare const getStyles: (props: ISpinButtonStyleProps) => ISpinButtonStyles;
