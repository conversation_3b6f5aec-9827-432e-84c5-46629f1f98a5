import * as React from 'react';
import type * as ReactTestUtils from 'react-dom/test-utils';
export declare function getBySelector(container: HTMLElement | Element, selector: string): Element | null;
export declare function getByAllSelector(container: HTMLElement | Element, selector: string): NodeListOf<Element>;
export declare function findNodesV2(container: HTMLElement, selector: string): HTMLElement[];
export declare function expectNodesV2(container: HTMLElement, className: string, n: number): void;
export declare function expectOneV2(container: HTMLElement, selector: string): void;
/** @deprecated Use fake timers and `jest.runAllTimers()` instead */
export declare function delay(millisecond: number): Promise<void>;
export declare function renderIntoDocument(element: React.ReactElement<any>): HTMLElement;
export declare function mockEvent(targetValue?: string): ReactTestUtils.SyntheticEventData;
/**
 * Hack for forcing Je<PERSON> to run pending promises
 * https://github.com/facebook/jest/issues/2157#issuecomment-897935688
 */
export declare function flushPromises(): Promise<void>;
/**
 * Verify that the given element and its parents do NOT have `aria-hidden` set.
 */
export declare function expectNoHiddenParents(element: HTMLElement): void;
