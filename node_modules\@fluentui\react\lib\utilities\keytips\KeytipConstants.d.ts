export declare const KTP_PREFIX = "ktp";
export declare const KTP_SEPARATOR = "-";
export declare const KTP_FULL_PREFIX: string;
export declare const DATAKTP_TARGET = "data-ktp-target";
export declare const DATAKTP_EXECUTE_TARGET = "data-ktp-execute-target";
export declare const DATAKTP_ARIA_TARGET = "data-ktp-aria-target";
export declare const KTP_LAYER_ID = "ktp-layer-id";
export declare const KTP_ARIA_SEPARATOR = ", ";
export declare namespace KeytipEvents {
    const KEYTIP_ADDED = "keytipAdded";
    const KEYTIP_REMOVED = "keytipRemoved";
    const KEYTIP_UPDATED = "keytipUpdated";
    const PERSISTED_KEYTIP_ADDED = "persistedKeytipAdded";
    const PERSISTED_KEYTIP_REMOVED = "persistedKeytipRemoved";
    const PERSISTED_KEYTIP_EXECUTE = "persistedKeytipExecute";
    const ENTER_KEYTIP_MODE = "enterKeytipMode";
    const EXIT_KEYTIP_MODE = "exitKeytipMode";
}
