import * as React from 'react';
import type { IPivotProps } from './Pivot.types';
/**
 * The Pivot control and related tabs pattern are used for navigating frequently accessed,
 * distinct content categories. Pivots allow for navigation between two or more content
 * views and relies on text headers to articulate the different sections of content.
 */
export declare const Pivot: React.FunctionComponent<IPivotProps>;
