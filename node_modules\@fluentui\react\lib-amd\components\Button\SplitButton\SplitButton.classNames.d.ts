import type { IButtonStyles } from '../Button.types';
export interface ISplitButtonClassNames {
    root?: string;
    icon?: string;
    splitButtonContainer?: string;
    flexContainer?: string;
    divider?: string;
}
export declare const SplitButtonGlobalClassNames: {
    msSplitButtonDivider: string;
};
export declare const getSplitButtonClassNames: (styles: IButtonStyles, disabled: boolean, expanded: boolean, checked: boolean, primaryDisabled?: boolean) => ISplitButtonClassNames;
