export { AnimationStyles, AnimationVariables, CommunicationColors, DefaultEffects, DefaultFontStyles, DefaultPalette, DefaultSpacing, Depths, FluentTheme, createFontStyles, createTheme, LocalizedFontFamilies, LocalizedFontNames, mergeThemes, MotionDurations, MotionTimings, MotionAnimations, NeutralColors, SharedColors, registerDefaultFontFaces, } from '@fluentui/theme';
export type { ComponentStyles, ComponentsStyles, IAnimationStyles, IAnimationVariables, IEffects, IFontStyles, IPalette, IPartialTheme, IScheme, ISchemeNames, ISemanticColors, ISemanticTextColors, ISpacing, ITheme, PartialTheme, Theme, } from '@fluentui/theme';
export * from './utilities/ThemeProvider/index';
