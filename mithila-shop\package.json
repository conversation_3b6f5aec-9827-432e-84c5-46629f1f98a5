{"name": "mithila-shop", "productName": "मैथिली विकास कोष - Mithila Shop Management", "version": "1.0.0", "description": "Offline shop management system for <PERSON><PERSON><PERSON> handcraft business - bags, sarees, paintings and traditional art", "main": "src/index.js", "scripts": {"start": "electron-forge start", "dev": "electron-forge start", "build": "tsc", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "eslint src --ext .ts,.js", "lint:fix": "eslint src --ext .ts,.js --fix", "format": "prettier --write \"src/**/*.{ts,js,html,css,scss}\"", "test": "jest", "test:watch": "jest --watch", "db:setup": "node scripts/setup-database.js", "db:migrate": "node scripts/migrate-database.js", "db:seed": "node scripts/seed-database.js"}, "keywords": ["mithila", "handcraft", "shop", "management", "offline", "electron", "sqlite", "bags", "sarees", "paintings"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"electron-squirrel-startup": "^1.0.1"}, "devDependencies": {"@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.8.1", "@electron-forge/maker-rpm": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.1", "@electron-forge/plugin-fuses": "^7.8.1", "@electron/fuses": "^1.8.0", "electron": "^37.1.0", "typescript": "^5.0.0", "@types/node": "^20.0.0", "@typescript-eslint/parser": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0", "ts-jest": "^29.0.0", "nodemon": "^3.0.0", "concurrently": "^8.0.0"}}